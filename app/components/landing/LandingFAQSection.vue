<script setup lang="ts">
const { t } = useI18n()

// Hardcoded FAQ questions from CSV
const allQuestions = [
  {
    title: t('Why choose geminigen.ai over other TTV tools?'),
    content: 'Geminigen.ai offers AI-generated images and videos from text at a more affordable price compared to other applications on the market. In addition to image and video generation from text, we also provide text-to-speech services and text-based conversation generation.'
  },
  {
    title: 'How do I use the geminigen.ai service?',
    content: 'Our service is designed to be user-friendly. Simply describe the video you want in text, and the system will automatically convert it into a video.'
  },
  {
    title: 'Do I need programming knowledge to use geminigen.ai?',
    content: 'No, you don\'t need any programming knowledge. We\'ve integrated Gemini\'s TTV API into our website, making the process of converting text into images and videos simple and convenient for everyone.'
  },
  {
    title: 'What languages are supported for text input?',
    content: 'We currently support multiple languages including English, Vietnamese, Spanish, and more. Support for additional languages is being added regularly.'
  },
  {
    title: 'What is the cost of using geminigen.ai?',
    content: 'We price based on Gemini\'s pricing, ensuring costs are lower than many other text-to-speech conversion tools on the market.'
  },
  {
    title: 'Can I use geminigen.ai for commercial purposes?',
    content: 'Yes, our service supports both personal and commercial purposes. However, please ensure compliance with our terms of use.'
  },
  {
    title: 'What is the quality of the video on geminigen.ai?',
    content: 'The generated images and videos are high-quality, with realistic and vivid visuals thanks to Gemini\'s advanced TTV technology.'
  },
  {
    title: 'How do I protect my privacy and data on geminigen.ai?',
    content: 'User security and privacy are our top priorities. We employ advanced security measures to protect your data and do not share information with any third parties without your consent.'
  },
  {
    title: 'For Text to Speech conversion, Can I edit or customize the output audio?',
    content: 'While we do not provide direct editing functions on the platform, you can customize some settings such as reading speed and tone before conversion. This allows you to have better control over the feel and final sound of the output file.'
  },
  {
    title: 'For Text to Speech conversion, Can I request additional voices or new languages?',
    content: 'We always listen to user feedback and strive to expand our services. If you have specific requests for a particular voice or language, please feel free to submit them to our support system.'
  },
  {
    title: 'Where can I contact technical support if I encounter issues using the service?',
    content: 'We provide support via email and live chat on the website. Our support team is always ready to answer any questions and assist you whenever needed.'
  },
  {
    title: 'Do I need to create an account to use the service?',
    content: 'Yes, creating an account helps you manage converted imagen, video, documents easily and access advanced features and better customer support services.'
  },
  {
    title: 'Can I use geminigen.ai to create content for my website or blog?',
    content: 'Yes, you can use our service to create audio content for your website, blog, or social media platforms, enriching the way you deliver information to your readers or customers.'
  },
  {
    title: 'What is the output file format of the imagen?',
    content: 'The primary output file format is png, which ensures compatibility with most devices.'
  },
  {
    title: 'How much does it cost to generate a video?',
    content: 'Each video generation is counted as one credit. You can purchase credits based on your needs from our pricing page.'
  },
  {
    title: 'What is the output file format of the speech?',
    content: 'The primary output file format is MP3,WAV, which ensures compatibility with most devices and music playback software.'
  },
  {
    title: 'Is the payment process difficult? What payment methods are available?',
    content: 'The payment process is very easy. We offer popular payment methods including PayPal, Debit, and Crypto'
  },
  {
    title: 'Are there any subscription plans for frequent users?',
    content: 'No, we sell credits without expiration date. When you run out of credits, buy another credit pack.'
  },
  {
    title: 'Will I get a refund if my video fails or has an error?',
    content: 'We will count credits when your video is successfully created. In case of errors, credits will not be counted.'
  }
]

// Group questions into categories for better organization
const faqData = computed(() => {
  const questionsPerCategory = Math.ceil(allQuestions.length / 3)

  return {
    title: t('faq.title'),
    description: t('faq.description'),
    categories: [
      {
        title: t('faq.general.title'),
        questions: allQuestions.slice(0, questionsPerCategory)
      },
      {
        title: t('faq.services.title'),
        questions: allQuestions.slice(questionsPerCategory, questionsPerCategory * 2)
      },
      {
        title: t('faq.pricing.title'),
        questions: allQuestions.slice(questionsPerCategory * 2)
      }
    ].filter(category => category.questions.length > 0)
  }
})

const selectedCategory = ref(0)
</script>

<template>
  <section class="py-24 bg-gradient-to-b from-muted/30 to-background">
    <UContainer class="max-w-6xl">
      <Motion
        :initial="{
          opacity: 0,
          y: 50
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8
        }"
        class="text-center mb-16"
      >
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
          {{ faqData.title }}
        </h2>
        <p class="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
          {{ faqData.description }}
        </p>
      </Motion>

      <!-- Category tabs -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.6,
          delay: 0.2
        }"
      >
        <div class="flex flex-wrap justify-center gap-4 mb-12">
          <button
            v-for="(category, index) in faqData.categories"
            :key="index"
            class="px-6 py-3 rounded-full font-medium transition-all duration-300"
            :class="selectedCategory === index
              ? 'bg-primary text-primary-foreground shadow-lg'
              : 'bg-card text-muted-foreground hover:bg-primary/10 hover:text-primary border border-border'"
            @click="selectedCategory = index"
          >
            {{ category.title }}
          </button>
        </div>
      </Motion>

      <!-- FAQ Content -->
      <Motion
        :key="selectedCategory"
        :initial="{
          opacity: 0,
          x: 20
        }"
        :animate="{
          opacity: 1,
          x: 0
        }"
        :transition="{
          duration: 0.5
        }"
      >
        <div class="space-y-4">
          <Motion
            v-for="(question, index) in faqData.categories[selectedCategory].questions"
            :key="index"
            :initial="{
              opacity: 0,
              y: 20
            }"
            :animate="{
              opacity: 1,
              y: 0
            }"
            :transition="{
              duration: 0.4,
              delay: index * 0.1
            }"
          >
            <UAccordion
              :items="[{
                label: question.title,
                content: question.content,
                defaultOpen: index === 0
              }]"
              :ui="{
                wrapper: 'space-y-0',
                item: {
                  base: 'border border-border rounded-lg overflow-hidden',
                  padding: 'p-0'
                },
                trigger: {
                  base: 'flex items-center gap-3 w-full text-left p-6 hover:bg-muted/50 transition-colors duration-200',
                  label: 'text-lg font-semibold'
                },
                content: {
                  base: 'text-muted-foreground px-6 pb-6 pt-0 leading-relaxed'
                },
                trailingIcon: {
                  base: 'w-5 h-5 text-muted-foreground transition-transform duration-200'
                }
              }"
              class="mb-4"
            />
          </Motion>
        </div>
      </Motion>

      <!-- Contact support -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8,
          delay: 0.6
        }"
        class="text-center mt-16"
      >
        <div class="p-8 rounded-2xl bg-gradient-to-r from-primary/10 to-violet-500/10 border border-primary/20">
          <h3 class="text-xl font-bold mb-4">
            {{ t('faq.contact.title') }}
          </h3>
          <p class="text-muted-foreground mb-6">
            {{ t('faq.contact.description') }}
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <UButton
              size="lg"
              color="primary"
              variant="solid"
              :label="t('faq.contact.email')"
              icon="lucide:mail"
              to="mailto:<EMAIL>"
              class="px-6"
            />
            <UButton
              size="lg"
              color="primary"
              variant="outline"
              :label="t('faq.contact.discord')"
              icon="i-simple-icons-discord"
              to="https://discord.com/channels/1396217701449338972/1396219648298717225"
              target="_blank"
              class="px-6"
            />
          </div>
        </div>
      </Motion>
    </UContainer>
  </section>
</template>

<style scoped>
/* Gradient text effect */
h2 {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)), rgb(var(--color-violet-500)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Tab hover effects */
button:hover {
  transform: translateY(-2px);
}

/* Accordion custom styling */
.UAccordion {
  background: rgb(var(--color-card));
}
</style>
