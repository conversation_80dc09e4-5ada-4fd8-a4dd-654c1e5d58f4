import { readFileSync } from 'fs'
import { join } from 'path'

export default defineEventHandler(async (event) => {
  try {
    // Read the CSV file from the content directory
    const csvPath = join(process.cwd(), 'content', 'faq.csv')
    const csvContent = readFileSync(csvPath, 'utf-8')
    
    // Parse CSV content
    const parseCSV = (csvText: string) => {
      const lines = csvText.trim().split('\n')
      const questions = []
      
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i]
        if (line.trim()) {
          // Handle CSV parsing with quoted fields
          const values = []
          let current = ''
          let inQuotes = false
          
          for (let j = 0; j < line.length; j++) {
            const char = line[j]
            if (char === '"') {
              inQuotes = !inQuotes
            } else if (char === ',' && !inQuotes) {
              values.push(current.trim())
              current = ''
            } else {
              current += char
            }
          }
          values.push(current.trim())
          
          if (values.length >= 2 && values[0] && values[1]) {
            questions.push({
              title: values[0].replace(/^"|"$/g, ''), // Remove surrounding quotes
              content: values[1].replace(/^"|"$/g, '') // Remove surrounding quotes
            })
          }
        }
      }
      
      return questions
    }
    
    const questions = parseCSV(csvContent)
    
    return questions
  } catch (error) {
    console.error('Error reading FAQ CSV:', error)
    return []
  }
})
